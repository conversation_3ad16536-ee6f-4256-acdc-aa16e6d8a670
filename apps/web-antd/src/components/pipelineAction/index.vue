<script lang="ts" setup>
import { useRouter } from 'vue-router';

import { Button, message, Popconfirm } from 'ant-design-vue';

import { actionPipelineApi } from '#/api/engine/engine';

const props = defineProps({
  action: {
    type: String,
    required: true,
  },
  pipelineId: {
    type: String,
    required: true,
  },
  pipelineName: {
    type: String,
    required: true,
  },
  callback: {
    type: Function,
    required: false,
    default: () => {},
  },
});

const router = useRouter();

const actionLabelMap: { [key: string]: string } = {
  view: '查看',
  start: '开始',
  retry: '重试',
  stop: '停止',
  delete: '删除',
};

const getActionLabel = (action: string) => {
  return actionLabelMap[action];
};

const handleAction = async (action: any, id: string) => {
  switch (action) {
    case 'delete': {
      console.warn('删除');
      break;
    }
    case 'start':
    case 'stop': {
      await actionPipelineApi(id, action).then(() => {
        message.success('操作成功');
        if (props.callback) {
          props.callback();
        }
      });
      break;
    }
    case 'view': {
      router.push(`/pipelines/detail/${id}`);
      break;
    }
    default: {
      break;
    }
  }
};
</script>

<template>
  <tempalte v-if="action === 'view'">
    <Button @click="handleAction('view', props.pipelineId)" class="mr-2">
      查看
    </Button>
  </tempalte>
  <template
    v-else-if="
      action === 'start' ||
      action === 'delete' ||
      action === 'stop' ||
      action === 'retry'
    "
  >
    <Popconfirm
      :title="`是否确认 ${getActionLabel(action)} 任务 ${props.pipelineName}`"
      ok-text="是"
      cancel-text="否"
      @confirm="handleAction(action, props.pipelineId)"
    >
      <Button
        class="mr-2"
        :type="action === 'start' || action === 'retry' ? 'primary' : 'default'"
        :danger="action === 'stop' || action === 'delete'"
      >
        {{ getActionLabel(action) }}
      </Button>
    </Popconfirm>
  </template>
  <Button v-else @click="handleAction(action, props.pipelineId)" class="mr-2">
    {{ getActionLabel(action) }}
  </Button>
</template>
