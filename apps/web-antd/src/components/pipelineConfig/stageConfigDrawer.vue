<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Form, FormItem, Input } from 'ant-design-vue';

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});

const data = ref();
</script>
<template>
  <Drawer append-to-main title="阶段详情" class="w-[900px]">
    <Form class="mt-4" :label-col="{ span: 3 }">
      <FormItem label="阶段序号" name="stageIndex">
        <Input placeholder="请输入阶段序号" :value="data.index" disabled />
      </FormItem>
      <FormItem label="阶段名称" name="stageName">
        <Input placeholder="请输入阶段名称" :value="data.name" disabled />
      </FormItem>
    </Form>
  </Drawer>
</template>
