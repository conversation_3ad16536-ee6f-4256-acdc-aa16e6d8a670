<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Form, FormItem, Input } from 'ant-design-vue';

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});

const data = ref();
</script>
<template>
  <Drawer append-to-main title="任务详情" class="w-[900px]">
    <Form class="mt-4" :label-col="{ span: 3 }">
      <FormItem label="阶段序号" name="stageIndex">
        <Input placeholder="请输入阶段序号" :value="data.stageIndex" disabled />
      </FormItem>
      <FormItem label="任务序号" name="taskIndex">
        <Input placeholder="请输入任务序号" :value="data.index" disabled />
      </FormItem>
      <FormItem label="任务名称" name="taskName">
        <Input placeholder="请输入任务名称" :value="data.name" disabled />
      </FormItem>
    </Form>
  </Drawer>
</template>
