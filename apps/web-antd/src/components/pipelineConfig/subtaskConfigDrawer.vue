<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Form, FormItem, Input } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const data = ref();

const gridOptions: VxeTableGridOptions = {
  columns: [
    {
      title: '参数名称',
      field: 'name',
    },
    {
      title: '参数键名',
      field: 'key',
    },
    {
      title: '参数值',
      field: 'value',
      showOverflow: false,
      formatter({ row }) {
        if (row.value && typeof row.value === 'object') {
          return JSON.stringify(row.value);
        }
        return row.value;
      },
    },
    {
      title: '参数类型',
      field: 'type',
      formatter({ row }) {
        if (row.type === 'plain') {
          return '普通参数';
        } else if (row.type === 'ref') {
          return '引用';
        }
        return '未知类型';
      },
    },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
};

const [ParamsGrid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      gridOptions.data = data.value.config.plugin.input;
      gridApi.setState({ gridOptions });
    }
  },
});
</script>
<template>
  <Drawer append-to-main title="子任务详情" class="w-[900px]">
    <Form class="mt-4" :label-col="{ span: 3 }">
      <FormItem label="子任务序号" name="index">
        <Input placeholder="请输入子任务序号" :value="data.index" disabled />
      </FormItem>
      <FormItem label="子任务名称" name="name">
        <Input placeholder="请输入子任务名称" :value="data.name" disabled />
      </FormItem>
      <FormItem label="子任务插件" name="plugin">
        <Input
          placeholder="请选择子任务插件"
          :value="data.config.plugin.name"
          disabled
        />
      </FormItem>
      <FormItem label="输入参数" name="inputParams">
        <ParamsGrid />
      </FormItem>
    </Form>
  </Drawer>
</template>
