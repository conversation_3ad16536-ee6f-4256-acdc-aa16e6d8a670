<script lang="ts" setup>
import { useVbenDrawer } from '@vben/common-ui';

import { Pipeline } from '#/components/pipeline';

import StageDetailDrawer from './stageDetailDrawer.vue';
import SubtaskDetailDrawer from './subtaskDetailDrawer.vue';
import TaskDetailDrawer from './taskDetailDrawer.vue';

const props = defineProps({
  pipelineData: {
    type: Object,
    required: true,
  },
});

const [StageContentDrawer, stageContentDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: StageDetailDrawer,
});

const [TaskContentDrawer, taskContentDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: TaskDetailDrawer,
});

const [SubtaskContentDrawer, subtaskContentDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: SubtaskDetailDrawer,
});

const handlePipelineChange = (newPipeline: any) => {
  console.error('Pipeline changed:', newPipeline);
};

const handlePipelineClick = (event: any) => {
  switch (event.type) {
    case 'stage': {
      stageContentDrawerApi
        .setData({
          index: event.stageIndex + 1,
          name: props.pipelineData.stages[event.stageIndex]?.name,
          status: props.pipelineData.stages[event.stageIndex]?.status,
        })
        .open();

      break;
    }
    case 'subtask': {
      subtaskContentDrawerApi
        .setData({
          index: event.subtaskIndex + 1,
          name: props.pipelineData.stages[event.stageIndex]?.tasks[
            event.taskIndex
          ]?.subtasks[event.subtaskIndex]?.name,
          config:
            props.pipelineData.stages[event.stageIndex]?.tasks[event.taskIndex]
              ?.subtasks[event.subtaskIndex],
        })
        .open();

      break;
    }
    case 'task': {
      taskContentDrawerApi
        .setData({
          stageIndex: event.stageIndex + 1,
          index: event.taskIndex + 1,
          name: props.pipelineData.stages[event.stageIndex]?.tasks[
            event.taskIndex
          ]?.name,
          status:
            props.pipelineData.stages[event.stageIndex]?.tasks[event.taskIndex]
              ?.status,
        })
        .open();

      break;
    }
  }
};
</script>

<template>
  <div class="scroll-container">
    <StageContentDrawer />
    <TaskContentDrawer />
    <SubtaskContentDrawer />
    <Pipeline
      class="ml-10 mt-4"
      :pipeline="props.pipelineData"
      :editable="false"
      :is-preview="false"
      :is-exec-detail="true"
      :is-latest-build="false"
      :can-skip-element="false"
      @change="handlePipelineChange"
      @click="handlePipelineClick"
    />
  </div>
</template>

<style>
.scroll-container::before {
  position: absolute;
  top: 40px;
  left: 0;
  min-width: calc(100% - 30px);
  height: 0;
  content: '';
  border-top: 2px dashed #c3cdd7;
}
</style>
