<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, ref } from 'vue';

import { <PERSON>sonViewer, useVbenDrawer } from '@vben/common-ui';

import {
  Descriptions,
  DescriptionsItem,
  Divider,
  Select,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { StatusTag } from '#/components/statusTag';

const data = ref();

const gridOptions: VxeTableGridOptions = {
  columns: [
    {
      title: '参数名称',
      field: 'name',
    },
    {
      title: '参数键名',
      field: 'key',
    },
    {
      title: '参数值',
      field: 'value',
      showOverflow: false,
      formatter({ row }) {
        if (row.value && typeof row.value === 'object') {
          return JSON.stringify(row.value);
        }
        return row.value;
      },
    },
    {
      title: '参数类型',
      field: 'type',
      formatter({ row }) {
        if (row.type === 'plain') {
          return '普通参数';
        } else if (row.type === 'ref') {
          return '引用';
        }
        return '未知类型';
      },
    },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
};

const [ParamsGrid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      gridOptions.data = data.value.config.plugin.input;
      selectedResult.value = data.value.config.plugin.results.length - 1;
      gridApi.setState({ gridOptions });
    }
  },
});

const selectedResult = ref(0);
const outputJson = computed(() => {
  return JSON.parse(
    data.value.config.plugin.results[selectedResult.value].output,
  );
});
const outputIsSuccess = computed(() => {
  return data.value.config.plugin.results[selectedResult.value].is_success;
});
</script>
<template>
  <Drawer append-to-main title="任务详情" class="w-[900px]">
    <Divider>基础信息</Divider>
    <Descriptions bordered :column="2">
      <DescriptionsItem label="子任务序号">{{ data.index }}</DescriptionsItem>
      <DescriptionsItem label="子任务名称">{{ data.name }}</DescriptionsItem>
      <DescriptionsItem label="子任务插件">
        {{ data.config.plugin.name }}
      </DescriptionsItem>
      <DescriptionsItem label="子任务状态">
        <StatusTag :status="data.config.status" />
      </DescriptionsItem>
    </Descriptions>
    <Divider>输入参数</Divider>
    <div>
      <ParamsGrid />
    </div>
    <Divider>输出参数</Divider>
    <div class="mb-2 ml-2">
      第
      <Select v-model:value="selectedResult">
        <Select.Option
          v-for="(_, index) in data.config.plugin.results"
          :key="index"
          :value="index"
        >
          {{ index + 1 }}
        </Select.Option>
      </Select>
      次执行, 状态:
      <Tag v-if="outputIsSuccess" color="green">成功</Tag>
      <Tag v-else color="red">失败</Tag>
    </div>
    <JsonViewer
      class="ml-2 mr-2"
      :value="outputJson"
      :expand-depth="3"
      copyable
      :sort="false"
      boxed
    />
  </Drawer>
</template>
