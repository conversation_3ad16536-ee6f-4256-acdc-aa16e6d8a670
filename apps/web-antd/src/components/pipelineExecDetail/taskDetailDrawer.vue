<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Descriptions, DescriptionsItem, Divider } from 'ant-design-vue';

import { StatusTag } from '#/components/statusTag';

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});

const data = ref();
</script>
<template>
  <Drawer append-to-main title="任务详情" class="w-[900px]">
    <Divider>基础信息</Divider>
    <Descriptions bordered :column="2">
      <DescriptionsItem label="阶段序号">
        {{ data.stageIndex }}
      </DescriptionsItem>
      <DescriptionsItem label="任务序号">{{ data.index }}</DescriptionsItem>
      <DescriptionsItem label="任务名称" :span="2">
        {{ data.name }}
      </DescriptionsItem>
      <DescriptionsItem label="阶段状态">
        <StatusTag :status="data.status" />
      </DescriptionsItem>
    </Descriptions>
  </Drawer>
</template>
