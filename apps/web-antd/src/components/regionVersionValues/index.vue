<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';
import { Input, Table } from 'ant-design-vue';
import { ref, watch } from 'vue';
import {
  getRegionDataApi,
} from '#/api/region/region';
interface VersionValue {
  key: string;
  name: string;
  value: string;
}

const modelValue = defineModel<VersionValue[]>('value', { required: true });
const props = defineProps<{
  region: string;
}>();
const loading = ref<boolean>(false);


// 表格列配置
const columns: TableColumnType[] = [
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '值', dataIndex: 'value', key: 'value' },
];

const getRegionDataInfo = async (region: string) => {
  if (!region) {
    return;
  }
  loading.value = true;
  await getRegionDataApi(region).then((res) => {
    loading.value = false;
    modelValue.value.map((item) => {
      const data = res[item.key];
      if (data && data !== '0' && data !== '') {
        item.value = data;
      }
      return item;
    });
  });
};

watch (
  () => props.region,
  async (val) => {
    await getRegionDataInfo(val);
  },
  { immediate: true },
);


</script>

<template>
  <Table
    :columns="columns"
    :data-source="modelValue"
    bordered
    size="small"
    :pagination="false"
    :loading="loading"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        {{ record.name }}
      </template>
      <template v-if="column.key === 'value'">
        <Input :value="record.value" />
      </template>
    </template>
  </Table>
</template>
