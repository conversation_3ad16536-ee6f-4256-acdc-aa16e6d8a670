<script setup lang="ts">
import type { HostInfo } from './type';

import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';

import { computed, nextTick, reactive, ref, watch } from 'vue';

import { Button, Card, Divider, Input, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import HostnameLabel from './HostnameLabel.vue';
import ModuleSelect from './ModuleSelect.vue';

const props = defineProps<{
  hosts: HostInfo[]; // 所有可选的主机列表
}>();

const modelValue = defineModel<HostInfo[]>('value', { required: true });
const visible = defineModel<boolean>('visible', { required: true });

// 计算未选择的主机列表
const unselectedHosts = computed(() => {
  return props.hosts.filter(
    (host) =>
      !modelValue.value.some((selected) => selected.hostname === host.hostname),
  );
});

// 搜索和筛选相关的状态
const searchKeyword = ref('');
const allModules = [
  'dp',
  'oa',
  'muip',
  'ns',
  'gate',
  'gs',
  'mail',
  'rank',
  'fight',
  'fightmgr',
  'match',
  'ufight',
  'room',
  'sns',
  'dg',
];
const selectedModule = ref<string[]>(allModules);
const selectedSearchKeyword = ref('');

// 处理范围搜索的正则表达式
const rangePattern = /(\w+)\[(\d+),(\d+)\]/;

const extractModule = (str: string) => {
  const splitParts = str.split('-');
  if (splitParts.length !== 5) {
    return '';
  }
  const wordpart = splitParts[4]?.match(/[a-z]+/gi);
  if (wordpart) {
    return wordpart[0];
  }
  return '';
};

const hostMatch = (host: HostInfo, keyword: string) => {
  if (!keyword) {
    return true;
  }
  const rangeMatch = keyword.match(rangePattern);
  if (!rangeMatch) {
    return host.hostname.toLowerCase().includes(keyword.toLowerCase());
  }
  const [, prefix, start, end] = rangeMatch;
  const hostNumber = Number(host.hostname.match(/(\d+)$/)?.[1]);
  const module = extractModule(host.hostname);
  return (
    module === prefix &&
    hostNumber >= Number(start) &&
    hostNumber <= Number(end)
  );
};

// 过滤主机列表的函数
function filterHosts(
  hosts: HostInfo[],
  keyword: string,
  modules?: string[],
): HostInfo[] {
  // 如果没有关键字和模块筛选，返回全部
  if (!keyword && !modules) {
    return hosts;
  }
  return hosts.filter((host) => {
    // 主机名筛选
    const hostNameMatch = hostMatch(host, keyword);
    // 兼容模块为空的情况
    if (!modules) {
      return hostNameMatch;
    } else if (modules.length === 0) {
      return false;
    }
    // 模块筛选
    const hostModule = extractModule(host.hostname);
    if (!allModules.includes(hostModule)) {
      return hostNameMatch;
    }

    const moduleMatch = modules.includes(hostModule);

    return moduleMatch && hostNameMatch;
  });
}

const displaySelectedHosts = computed(() =>
  filterHosts(modelValue.value, selectedSearchKeyword.value),
);

const displayUnselectedHosts = computed(() => {
  return filterHosts(
    unselectedHosts.value,
    searchKeyword.value,
    selectedModule.value,
  );
});

const unSelectedgridOptions: VxeGridProps<HostInfo> = {
  height: 300,
  checkboxConfig: {
    highlight: true,
    range: true,
  },
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { type: 'checkbox', width: 60 },
    { field: 'hostname', title: '主机名', sortable: true },
    { field: 'ip', title: 'IP地址', sortable: true },
  ],
  data: displayUnselectedHosts.value,
  pagerConfig: {
    enabled: false,
  },
  sortConfig: {
    multiple: true,
  },
};

const selectedgridOptions: VxeGridProps<HostInfo> = {
  height: 425,
  checkboxConfig: {
    highlight: true,
    range: true,
  },
  columns: [
    { type: 'checkbox', width: 60 },
    { field: 'hostname', title: '主机名', sortable: true },
    { field: 'ip', title: 'IP地址', sortable: true },
  ],
  data: displaySelectedHosts.value,
  pagerConfig: {
    enabled: false,
  },
  sortConfig: {
    multiple: true,
  },
};

const leftSelectedRows = ref<HostInfo[]>([]);
const rightSelectedRows = ref<HostInfo[]>([]);

const unSelectedgridEvents: VxeGridListeners<HostInfo> = {
  checkboxChange: ({ records }: { records: HostInfo[] }) => {
    leftSelectedRows.value = records;
  },
  checkboxAll: ({ records }: { records: HostInfo[] }) => {
    leftSelectedRows.value = records;
  },
};

const selectedgridEvents: VxeGridListeners<HostInfo> = {
  checkboxChange: ({ records }: { records: HostInfo[] }) => {
    rightSelectedRows.value = records;
  },
  checkboxAll: ({ records }: { records: HostInfo[] }) => {
    rightSelectedRows.value = records;
  },
};

const [UnSelectedGrid, unSelectedGridApi] = useVbenVxeGrid(
  reactive({
    gridEvents: unSelectedgridEvents,
    gridOptions: unSelectedgridOptions,
  }),
);

const [SelectedGrid, selectedGridApi] = useVbenVxeGrid({
  gridEvents: selectedgridEvents,
  gridOptions: selectedgridOptions,
});

const handleSearch = () => {
  // 搜索时重置选中状态
  unSelectedGridApi.grid.reloadData(displayUnselectedHosts.value);
  leftSelectedRows.value = [];
};

const handleSelectedSearch = () => {
  // 搜索时重置选中状态
  selectedGridApi.grid.reloadData(displaySelectedHosts.value);
  rightSelectedRows.value = [];
};

const handleAdd = () => {
  const moveHosts = unselectedHosts.value.filter((host) =>
    leftSelectedRows.value.map((host) => host.hostname).includes(host.hostname),
  );
  modelValue.value.push(...moveHosts);
  leftSelectedRows.value = [];
  nextTick(() => {
    // 没找到这里什么问题，只能用这种方法更新左边的选择框
    unSelectedgridOptions.data = displayUnselectedHosts.value;
    unSelectedGridApi.setState({ gridOptions: unSelectedgridOptions });
    selectedGridApi.grid.reloadData(displaySelectedHosts.value);
  });
};

const handleRemove = () => {
  const removedHosts = new Set(
    rightSelectedRows.value.map((host) => host.hostname),
  );
  console.log('[handleRemove] modelValue filter result is: ',modelValue.value.filter(
    (item) => !removedHosts.has(item.hostname),
  ), 'modelValue.value is: ', modelValue.value);
  modelValue.value = modelValue.value.filter(
    (item) => !removedHosts.has(item.hostname),
  );
  console.log('[handleRemove] removedHosts is :', removedHosts);
  console.log('[handleRemove] modelValue is :', modelValue.value);
  rightSelectedRows.value = [];
  nextTick(() => {
    unSelectedGridApi.grid.reloadData(displayUnselectedHosts.value);
    selectedGridApi.grid.reloadData(displaySelectedHosts.value);
  });
};

const handleOk = () => {
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};

// 快速选择处理函数
type QuickSelectType =
  | 'offlineInternalPlayers'
  | 'restartNs'
  | 'startGate'
  | 'startMailGs'
  | 'startNonPublic'
  | 'startResourceDependent';

function handleQuickSelect(type: QuickSelectType) {
  let moveHosts: HostInfo[] = [];
  switch (type) {
    case 'offlineInternalPlayers': {
      moveHosts = unselectedHosts.value.filter((item) => {
        const module = extractModule(item.hostname);
        return !['dp', 'muip', 'ns', 'oa'].includes(module);
      });
      break;
    }
    case 'restartNs': {
      moveHosts = unselectedHosts.value.filter((item) => {
        const module = extractModule(item.hostname);
        return module === 'ns';
      });
      break;
    }
    case 'startGate': {
      moveHosts = unselectedHosts.value.filter((item) => {
        const module = extractModule(item.hostname);
        return module === 'gate';
      });
      break;
    }
    case 'startMailGs': {
      moveHosts = unselectedHosts.value.filter((item) => {
        const module = extractModule(item.hostname);
        return module === 'mail' || module === 'gs';
      });
      break;
    }
    case 'startNonPublic': {
      moveHosts = unselectedHosts.value.filter((item) => {
        const module = extractModule(item.hostname);
        return !['dp', 'gate', 'muip', 'ns', 'oa'].includes(module);
      });
      break;
    }
    case 'startResourceDependent': {
      moveHosts = unselectedHosts.value.filter((item) => {
        const module = extractModule(item.hostname);
        return !['dp', 'gate', 'gs', 'mail', 'muip', 'ns', 'oa'].includes(
          module,
        );
      });
      break;
    }
  }
  modelValue.value.push(...moveHosts);
}

// 监听模块选择变化
watch(selectedModule, () => {
  // 模块选择变化时重置选中状态
  unSelectedGridApi.grid.clearCheckboxRow();
});

// 监听 modelValue 变化，确保移除 _X_ROW_KEY
watch(modelValue, (newVal) => {
  console.log('[Modal]modelValue is :', newVal);
  if (newVal && newVal.some(item => '_X_ROW_KEY' in item)) {
    // 如果发现有 _X_ROW_KEY，清理它们
    modelValue.value = newVal.map(item => {
      const { hostname, ip, agent_id } = item;
      return { hostname, ip, agent_id };
    });
  }
}, { deep: true });
</script>

<template>
  <Modal
    :open="visible"
    title="选择区服机器"
    width="1400px"
    destry-on-close
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="host-selection-container">
      <!-- 左侧未选择区域 -->
      <Card class="selection-card" :bordered="true">
        <template #title>
          <div class="card-title">
            <span>未选择</span>
            <span class="count">
              {{ leftSelectedRows.length }}/{{ unselectedHosts.length }}
            </span>
          </div>
        </template>

        <div>
          <!-- 固定区域 -->
          <div class="fixed-content">
            <div class="search-area">
              <div class="input-row">
                <HostnameLabel />
                <Input
                  placeholder="请输入关键字按回车进行搜索"
                  v-model:value="searchKeyword"
                  @change="handleSearch"
                />
              </div>
            </div>

            <div class="module-area">
              <div class="input-row">
                <span class="label">模块</span>
                <ModuleSelect :value="selectedModule" />
              </div>
            </div>

            <div class="quick-select-area">
              <div class="input-row">
                <span class="label">快速选择</span>
                <div class="button-group">
                  <Button @click="handleQuickSelect('offlineInternalPlayers')">
                    内网玩家下线
                  </Button>
                  <Button @click="handleQuickSelect('restartNs')">
                    重启ns模块
                  </Button>
                  <Button @click="handleQuickSelect('startNonPublic')">
                    启动非对外模块
                  </Button>
                  <Button @click="handleQuickSelect('startGate')">
                    启动gate模块
                  </Button>
                  <Button @click="handleQuickSelect('startResourceDependent')">
                    启动仅依赖数值/资源模块
                  </Button>
                  <Button @click="handleQuickSelect('startMailGs')">
                    启动mail/gs
                  </Button>
                </div>
              </div>
            </div>

            <Divider class="custom-divider" />
          </div>

          <!-- 表格区域 -->
          <div class="table-container">
            <UnSelectedGrid />
          </div>
        </div>
      </Card>

      <!-- 中间按钮区域 -->
      <div class="action-buttons-container">
        <Button type="primary" @click="handleAdd"> {{ '添加 >' }}</Button>
        <Button @click="handleRemove"> {{ '< 移除' }}</Button>
      </div>

      <!-- 右侧已选择区域 -->
      <Card class="selection-card" :bordered="true">
        <template #title>
          <div class="card-title">
            <span>已选择</span>
            <span class="count">
              {{ rightSelectedRows.length }}/{{ modelValue.length }}
            </span>
          </div>
        </template>

        <div>
          <!-- 固定区域 -->
          <div class="fixed-content">
            <div class="input-row">
              <HostnameLabel />
              <Input
                placeholder="请输入关键字按回车进行搜索"
                v-model:value="selectedSearchKeyword"
                @change="handleSelectedSearch"
              />
            </div>
          </div>
          <Divider class="custom-divider" />
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <SelectedGrid />
        </div>
      </Card>
    </div>
  </Modal>
</template>

<style scoped>
.host-selection-container {
  display: flex;
  gap: 20px;
}

.selection-card {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 620px;
}

:deep(.ant-card-body) {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.fixed-content {
  flex-shrink: 0; /* 防止固定内容被压缩 */
}

.custom-divider {
  margin: 12px 0;
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.input-row {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 16px;
}

.label {
  display: flex;
  align-items: center;
  min-width: 60px;
  white-space: nowrap;
}

/* 新增模块标签的特殊样式 */
.module-area .label {
  justify-content: flex-end; /* 让模块文字靠右对齐 */
  min-width: 60px; /* 确保宽度与其他标签一致 */
}

.search-area,
.module-area {
  .input-row :deep(.ant-input),
  .input-row :deep(.ant-select) {
    flex: 1;
  }
}

.module-area {
  margin-bottom: 16px;
}

.quick-select-area {
  .input-row {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .button-group {
    display: grid;
    flex: 1;
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  :deep(.ant-btn) {
    text-align: center;
    white-space: nowrap;
  }
}

.action-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  justify-content: center;
}

.count {
  color: #999;
}

.help-icon {
  margin-left: 4px;
  font-size: 14px;
  color: rgb(0 0 0 / 45%);
  cursor: help;
}

/* vxe-table 样式调整 */
:deep(.vxe-table) {
  border: none;
}

:deep(.vxe-table--header) {
  background-color: #fafafa;
}

:deep(.vxe-table--body) {
  background-color: #fff;
}

:deep(.vxe-body--row.row--checked) {
  background-color: #e6f7ff;
}

.table-container {
  flex: 1;
  margin-top: 8px;
  overflow: hidden; /* 防止表格溢出 */
}

:deep(.vxe-table--main-wrapper) {
  border: 1px solid #f0f0f0;
}

:deep(.vxe-table--body-wrapper) {
  overflow-y: auto !important;
}
</style>
