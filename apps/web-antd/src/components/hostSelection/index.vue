<script setup lang="ts">
import type { HostInfo } from './type';
import { ref, watch } from 'vue';
import { Button } from 'ant-design-vue';
import HostSelectionModal from './Modal.vue';

// 定义 props
defineProps<{
  hosts: HostInfo[]; // 所有可选的主机列表
}>();

const value = defineModel<HostInfo[]>('value', { required: true });
// 控制弹窗显示
const visible = ref(false);
watch(value.value, (newVal) => {
  console.log('[index]value is :', newVal);
});

</script>

<template>
  <div>
    <Button type="link" @click="visible = true" style="padding: 0">
      选择机器
      <template v-if="value.length > 0">
        (已选择 {{ value.length }} 台)
      </template>
    </Button>
    <!-- modal 使用后销毁 -->
    <HostSelectionModal
      v-if="visible"
      :hosts="hosts"
      v-model:value="value"
      v-model:visible="visible"
    />
  </div>
</template>
