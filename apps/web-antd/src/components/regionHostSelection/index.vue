<script setup lang="ts">
import { getRegionHostsApi } from '#/api/region/region';
import { ref, watch } from 'vue';
import type { HostInfo } from '../hostSelection/type';
import { HostSelection } from '../hostSelection';

const props = defineProps<{
  region: string;
}>();

const value = defineModel<HostInfo[]>('value', { required: true });
const hosts = ref<HostInfo[]>([]);

const getRegionHosts = async (region: string) => {
  await getRegionHostsApi(region)
    .then((res) => {
      hosts.value = res.list;
    })
    .finally(() => {});
};

watch(
  () => props.region,
  async (val) => {
    if (!val) {
      return;
    }
    await Promise.all([getRegionHosts(val)]);
  },
  { immediate: true },
);
</script>

<template>
  <HostSelection :hosts="hosts" v-model:value="value" />
</template>
