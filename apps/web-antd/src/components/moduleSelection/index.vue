<script lang="ts" setup>
import type { MenuProps } from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';

import type { RegionPatchInfo } from '#/api/region/type';
import type { HostInfo } from '#/components/hostSelection/type';

import { computed, ref, watch } from 'vue';

import { DownOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Dropdown,
  Input,
  Menu,
  MenuItem,
  Select,
  Table,
} from 'ant-design-vue';

import { HostSelection } from '#/components/hostSelection';
import {
  getRegionPatchApi,
  getRegionHostsApi
} from '#/api/region/region';
// 定义模块类型
export interface ModuleHost {
  module: string;
  hosts: HostInfo[];
  process_num: number;
  patch?: string;
}

// 定义组件属性
interface Props {
  region: string;
  showPatchId?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showPatchId: true,
});

// 使用defineModel来实现双向绑定
const moduleHosts = defineModel<ModuleHost[]>('value', { required: true });
const hosts = ref<HostInfo[]>([]);

// 模块选项
const moduleOptions = [
  { value: 'dp', label: 'dispatch' },
  { value: 'muip', label: 'muipserver' },
  { value: 'oa', label: 'oaserver' },
  { value: 'ns', label: 'nodeserver' },
  { value: 'gs', label: 'gameserver' },
  { value: 'rank', label: 'rankserver' },
  { value: 'gate', label: 'gateserver' },
  { value: 'mail', label: 'mailserver' },
  { value: 'sns', label: 'snsserver' },
  { value: 'fightmgr', label: 'fightmgrserver' },
  { value: 'match', label: 'matchserver' },
  { value: 'dg', label: 'dbgate' },
  { value: 'ufight', label: 'ufightserver' },
  { value: 'fight', label: 'fightserver' },
  { value: 'room', label: 'roomserver' },
];

// 计算未选择的模块选项
const computedOptions = computed(() => {
  return moduleOptions.filter((item) => {
    if (moduleHosts.value.length === 0) {
      return true;
    }
    return !moduleHosts.value.some((module) => module.module === item.label);
  });
});

// 动态生成表格列配置
const moduleColumns = computed<TableColumnType[]>(() => {
  const columns: TableColumnType[] = [
    { title: '模块名称', dataIndex: 'module', key: 'module' },
    { title: '模块机器', dataIndex: 'hosts', key: 'hosts' },
  ];

  // 根据showPatchId属性决定是否显示patch列
  if (props.showPatchId) {
    columns.push({
      title: 'patch',
      dataIndex: 'patch',
      key: 'patch',
      width: 600,
    });
  }

  columns.push(
    {
      title: '进程数',
      dataIndex: 'process_num',
      key: 'process_num',
      width: 100,
    },
    { title: '操作', key: 'operation' },
  );

  return columns;
});

// 删除模块
const deleteModule = (record: ModuleHost) => {
  const index = moduleHosts.value.indexOf(record);
  if (index !== -1) {
    moduleHosts.value.splice(index, 1);
  }
};

// 添加模块
const addModule: MenuProps['onClick'] = (e) => {
  moduleHosts.value.push({
    module: String(e.key),
    hosts: [],
    process_num: 1,
  });
};

const regionPatchLoading = ref<boolean>(false);
const regionModulePatchInfoOptions = ref<Map<string, RegionPatchInfo[]>>(
  new Map(),
);
const getRegionPatchInfoOption = async (region: string) => {
  regionPatchLoading.value = true;
  await getRegionPatchApi(region).then((data: any) => {
    regionPatchLoading.value = false;
    regionModulePatchInfoOptions.value = new Map<string, RegionPatchInfo[]>();
    for (const cur of data) {
      const moduleName = cur.module;
      const patchOptions = cur.patch_infos.map((item: any) => ({
        update_time: item.update_time,
        patch_id: item.patch_id,
      }));
      regionModulePatchInfoOptions.value.set(moduleName, patchOptions);
    }
  });
};

const getRegionHosts = async (region: string) => {
  await getRegionHostsApi(region)
    .then((res) => {
      hosts.value = res.list;
    })
    .finally(() => {});
};

watch(
  () => props.region,
  async (val) => {
    if (!val) {
      return;
    }
    await Promise.all([
      getRegionHosts(val),
      getRegionPatchInfoOption(val),
    ]);
  },
  { immediate: true },
);

</script>

<template>
  <div>
    <Table
      v-if="moduleHosts.length > 0"
      :columns="moduleColumns"
      :data-source="moduleHosts"
      size="small"
      :pagination="false"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'module'">
          {{ record.module }}
        </template>
        <template v-if="column.key === 'hosts'">
          <HostSelection :hosts="hosts" :value="record.hosts" />
        </template>
        <template v-if="column.key === 'patch' && showPatchId">
          <Select
            v-model:value="record.patch"
            class="w-full"
            allow-clear
            :loading="regionPatchLoading"
          >
            <Select.Option
              v-for="option in regionModulePatchInfoOptions.get(record.module)"
              :key="option.patch_id"
              :value="option.patch_id"
            >
              {{ option.patch_id }}({{ option.update_time }})
            </Select.Option>
          </Select>
        </template>
        <template v-if="column.key === 'process_num'">
          <Input v-model:value="record.process_num" />
        </template>
        <template v-if="column.key === 'operation'">
          <Button type="link" @click="deleteModule(record as ModuleHost)"> 删除 </Button>
        </template>
      </template>
    </Table>
    <Dropdown trigger="click">
      <template #overlay>
        <Menu @click="addModule">
          <MenuItem v-for="option in computedOptions" :key="option.label">
            {{ option.label }}
          </MenuItem>
        </Menu>
      </template>
      <Button class="w-full" type="dashed">
        添加模块
        <DownOutlined />
      </Button>
    </Dropdown>
  </div>
</template>
