<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createRegionApi } from '#/api/region/region';

defineOptions({
  name: 'CraeteRegionModal',
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'name',
      label: '名称',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'data_branch',
      label: '数值分支',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'code_branch',
      label: '代码分支',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'description',
      label: '描述',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  title: '创建区服表单',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  createRegionApi({
    name: values.name,
    data_branch: values.data_branch,
    code_branch: values.code_branch,
    description: values.description,
  }).then(() => {
    modalApi.close();
    message.success({
      content: `创建区服${values.name}成功`,
      duration: 2,
    });
    const { callback } = modalApi.getData();
    callback();
  });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
