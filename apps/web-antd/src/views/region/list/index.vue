<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, Tag, Tooltip } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { queryRegionsApi } from '#/api/region/region';
import { queryRegionGroupsApi } from '#/api/region_group/region_group';

import CreateRegionModal from './createRegionModal.vue';

const regionGroupOptions = ref([]);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '区服名称',
    },
    {
      component: 'Select',
      fieldName: 'region_group',
      label: '区服分组',
      componentProps: {
        allowClear: true,
        options: regionGroupOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: false,
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    {
      title: '区服名称',
      field: 'name',
    },
    {
      title: '数值分支',
      field: 'data_branch',
    },
    {
      title: '代码分支',
      field: 'code_branch',
    },
    {
      title: 'sdk环境',
      field: 'sdk_env',
    },
    {
      title: '进程状态',
      field: 'process_info',
      slots: { default: 'process_info' },
    },
    {
      title: '停服状态',
      field: 'stop_info',
      slots: { default: 'stop_info' },
    },
    {
      title: '区服人数',
      field: 'user_count',
      formatter: ({ row }: { row: any }) => {
        return row.region_process_info.user_count === -1
          ? '未知'
          : row.region_process_info.user_count;
      },
    },
    {
      title: '占用情况',
      field: 'occupy_info',
      align: 'left',
      width: 300,
      slots: { default: 'occupy_info' },
    },
    {
      title: '区服描述',
      field: 'description',
      formatter: ({ row }: { row: any }) => {
        return row.description || '-';
      },
    },
    {
      title: '创建人',
      field: 'created_by',
      visible: false,
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    {
      title: '创建时间',
      field: 'created_at',
      visible: false,
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    { title: '操作', field: 'actions', slots: { default: 'actions' } },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        await getRegionGroupsOptions();
        return await queryRegionsApi({
          name: formValues.name,
          region_group: formValues.region_group,
          page: page.currentPage,
          size: page.pageSize,
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const getRegionGroupsOptions = async () => {
  const res = await queryRegionGroupsApi({ page: 1, size: 1000 });
  regionGroupOptions.value = res.list.map((item: any) => {
    return {
      label: item.name,
      value: item.code,
    };
  });
};

const handleCreate = () => {
  createFormModalApi
    .setData({
      callback: gridApi.reload,
    })
    .open();
};

const [CreateFormModal, createFormModalApi] = useVbenModal({
  connectedComponent: CreateRegionModal,
});
</script>

<template>
  <Page auto-content-height title="区服列表" description="展示所有区服">
    <CreateFormModal />
    <Grid>
      <template #process_info="{ row }">
        <template v-if="row.region_process_info.is_health">
          <Tag color="green"> 正常 </Tag>
        </template>
        <template v-else>
          <Tag color="error">
            异常(
            {{
              `${row.region_process_info.un_health_count}/${row.region_process_info.health_count + row.region_process_info.un_health_count}`
            }})
          </Tag>
        </template>
      </template>
      <template #stop_info="{ row }">
        <template v-if="!row.region_stop_info.is_region_stop">
          <!-- <Tag color="blue">未停服</Tag> -->
          -
        </template>
        <template v-else>
          <Tooltip placement="top">
            <template #title>
              <p>开始时间: {{ row.region_stop_info.stop_begin_time }}</p>
              <p>结束时间: {{ row.region_stop_info.stop_end_time }}</p>
            </template>
            <Tag color="error"> 已停服 </Tag>
          </Tooltip>
        </template>
      </template>
      <template #occupy_info="{ row }">
        <div v-if="row.region_occupy">
          <p>{{ row.region_occupy.occupy_msg }}</p>
          <p>
            {{ row.region_occupy.occupy_begin_time }} ~
            {{ row.region_occupy.occupy_end_time }}
          </p>
        </div>
        <template v-else>
          <!-- <Tag color="blue">未占用</Tag> -->
          -
        </template>
      </template>
      <template #toolbar-tools>
        <Button type="primary" @click="handleCreate">创建</Button>
      </template>
      <!-- <template #actions="{ row }">
        <Button @click="handleAction('view', row.pipeline.id)" class="mr-2">
          查看
        </Button>
        <template v-for="action in row.actions" :key="action">
          <template
            v-if="
              action === 'start' || action === 'delete' || action === 'stop'
            "
          >
            <Popconfirm
              :title="`是否确认 ${getActionLabel(action)} 任务 ${row.pipeline.name}`"
              ok-text="是"
              cancel-text="否"
              @confirm="handleAction(action, row.pipeline.id)"
            >
              <Button
                class="mr-2"
                :type="action === 'start' ? 'primary' : 'default'"
                :danger="action === 'stop' || action === 'delete'"
              >
                {{ getActionLabel(action) }}
              </Button>
            </Popconfirm>
          </template>
          <Button
            v-else
            @click="handleAction(action, row.pipeline.id)"
            class="mr-2"
          >
            {{ getActionLabel(action) }}
          </Button>
        </template>
      </template> -->
    </Grid>
  </Page>
</template>
