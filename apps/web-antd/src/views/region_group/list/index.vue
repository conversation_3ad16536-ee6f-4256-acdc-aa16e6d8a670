<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeGridListeners,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Popconfirm } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRegionsNamesApi } from '#/api/region/region';
import {
  batchDeleteRegionGroupsApi,
  deleteRegionGroupsApi,
  queryRegionGroupsApi,
} from '#/api/region_group/region_group';

import CreateGroupModal from './createGroupModal.vue';
import DisplayGroupRegionsModal from './displayRegionsModal.vue';
import EditGroupModal from './editGroupModal.vue';
import EditGroupRegionsModal from './editRegionsModal.vue';

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '区服分组名称',
    },
    {
      component: 'Input',
      fieldName: 'code',
      label: '区服分组代号',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const selectedRows = ref<any[]>([]);
const regionNameOptions = ref([]);

const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    highlight: true,
    range: true,
  },
  columns: [
    { type: 'checkbox', width: 60 },
    {
      title: '名称',
      field: 'name',
    },
    {
      title: '包含区服个数',
      field: 'regions_count',
      slots: { default: 'regions_count' },
    },
    {
      title: '代号',
      field: 'code',
    },
    {
      title: '描述',
      field: 'description',
      formatter: ({ cellValue }) => {
        return cellValue || '-';
      },
    },
    {
      title: '创建人',
      field: 'created_by',
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    {
      title: '创建时间',
      field: 'created_at',
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    {
      title: '操作',
      field: 'actions',
      width: 330,
      slots: { default: 'actions' },
    },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        getRegionsNameOptions();
        return await queryRegionGroupsApi({
          name: formValues.name,
          code: formValues.code,
          page: page.currentPage,
          size: page.pageSize,
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const GridEvents: VxeGridListeners = {
  checkboxChange: () => {
    selectedRows.value = gridApi.grid.getCheckboxRecords();
  },
  checkboxAll: () => {
    selectedRows.value = gridApi.grid.getCheckboxRecords();
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: GridEvents,
});

const [CreateFormModal, createFormModalApi] = useVbenModal({
  connectedComponent: CreateGroupModal,
});

const [EditFormModal, editFormModalApi] = useVbenModal({
  connectedComponent: EditGroupModal,
});

const [EditRegionsModal, editRegionsModalApi] = useVbenModal({
  connectedComponent: EditGroupRegionsModal,
});

const [DisplayRegionsModal, displayRegionsModalApi] = useVbenModal({
  connectedComponent: DisplayGroupRegionsModal,
});

const handleCreate = () => {
  createFormModalApi
    .setData({
      callback: gridApi.reload,
    })
    .open();
};

const handleEdit = (row: any) => {
  editFormModalApi
    .setData({
      callback: gridApi.reload,
      row,
    })
    .open();
};

const getRegionsNameOptions = async () => {
  getRegionsNamesApi().then((res) => {
    regionNameOptions.value = res.list.map((item: string) => ({
      label: item,
      value: item,
    }));
  });
};
const handleAddRegions = (row: any) => {
  const currentRegionNames = new Set(row.regions.map((item: any) => item.name));
  editRegionsModalApi
    .setData({
      callback: gridApi.reload,
      mode: 'add',
      options: regionNameOptions.value.filter(
        (item: any) => !currentRegionNames.has(item.value),
      ),
      regionGroupCode: row.code,
    })
    .open();
};

const handleRemoveRegions = (row: any) => {
  const currentRegionNameOptions = row.regions.map((item: any) => ({
    label: item.name,
    value: item.name,
  }));
  editRegionsModalApi
    .setData({
      callback: gridApi.reload,
      mode: 'remove',
      options: currentRegionNameOptions,
      regionGroupCode: row.code,
    })
    .open();
};

const handleDelete = (row: any) => {
  deleteRegionGroupsApi(row.code).then(() => {
    message.success('删除成功');
    gridApi.reload();
  });
};

const handleBatchDelete = () => {
  const deleteCodes = gridApi.grid
    .getCheckboxRecords()
    .map((item) => item.code);
  batchDeleteRegionGroupsApi({
    region_group_codes: deleteCodes,
  }).then(() => {
    message.success('批量删除成功');
    gridApi.reload();
  });
};

const handleDisplayRegions = (row: any) => {
  const { code, regions } = row;
  displayRegionsModalApi
    .setData({
      options: regionNameOptions,
      regionGroupCode: code,
      regions: regions.map((item: any) => item.name),
    })
    .open();
};
</script>

<template>
  <Page auto-content-height title="区服分组列表" description="展示所有区服分组">
    <CreateFormModal />
    <EditFormModal />
    <EditRegionsModal />
    <DisplayRegionsModal />
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="handleCreate">创建</Button>
        <Popconfirm
          ok-text="是"
          cancel-text="否"
          title="是否确认批量删除"
          :disabled="selectedRows.length === 0"
          @confirm="handleBatchDelete"
        >
          <Button class="ml-2" danger :disabled="selectedRows.length === 0">
            批量删除
          </Button>
        </Popconfirm>
      </template>
      <template #regions_count="{ row }">
        <Button type="link" @click="handleDisplayRegions(row)">
          {{ row.regions.length }}
        </Button>
      </template>
      <template #actions="{ row }">
        <Button type="link" @click="handleEdit(row)" class="mr-1">
          编辑
        </Button>
        <Button type="link" @click="handleAddRegions(row)" class="mr-1">
          新增区服
        </Button>
        <Button type="link" @click="handleRemoveRegions(row)" class="mr-1">
          移除区服
        </Button>
        <Popconfirm
          ok-text="是"
          :title="`是否确认删除分组 ${row.code}`"
          cancel-text="否"
          @confirm="handleDelete(row)"
        >
          <Button type="link" danger> 删除 </Button>
        </Popconfirm>
      </template>
    </Grid>
  </Page>
</template>
