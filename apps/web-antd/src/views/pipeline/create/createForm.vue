<script lang="ts" setup>
import type { SelectValue } from 'ant-design-vue/es/select';

import type { SelectOption } from '@vben/types';

import type { FormData } from './type';

import type { HostInfo } from '#/components/hostSelection/type';

import { computed, defineComponent, onMounted, ref } from 'vue';

import { useDebounceFn } from '@vueuse/core';
import {
  Button,
  Divider,
  Dropdown,
  Form,
  FormItem,
  Input,
  InputNumber,
  InputPassword,
  RadioButton,
  RadioGroup,
  Select,
  Table,
  Tag,
} from 'ant-design-vue';

import {
  getRegionIdleHosts,
  getRegionMysql,
  getRegionMysqlIdx,
  getRegionRedis,
  getRegionRedisIdx,
} from '#/api/region/region';
import { HostSelection } from '#/components/hostSelection';

const formData = defineModel<FormData>('formData', { required: true });
const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

const regionHosts = ref<HostInfo[]>([]);
const mysqlOptions = ref<SelectOption[]>([]);
const redisOptions = ref<SelectOption[]>([]);
const allMysqlIdx = ref<SelectOption[]>([]);
const allRedisIdx = ref<SelectOption[]>([]);
const mysqlLoading = ref<boolean>(false);
const redisLoading = ref<boolean>(false);
const mysqlIdxLoading = ref<boolean>(false);
const redisIdxLoading = ref<boolean>(false);

const mysqlIdxOptions = computed(() => {
  const idxs = new Set(
    formData.value.mysqlInfos.flatMap((item) => item.table_idx),
  );
  return allMysqlIdx.value.filter((item) => !idxs.has(item.value));
});

const redisIdxOption = computed(() => {
  const idxs = new Set(formData.value.redisInfos.flatMap((item) => item.idx));
  return allRedisIdx.value.filter((item) => !idxs.has(item.value));
});

// 表格列配置
const mysqlInfoColumns = [
  { title: '实例', dataIndex: 'instance_id', key: 'instance_id', width: 400 },
  { title: '用户名', dataIndex: 'user', key: 'user' },
  { title: '密码', dataIndex: 'pwd', key: 'pwd' },
  { title: '索引', dataIndex: 'idx', key: 'idx', width: 650 },
  { title: '操作', key: 'operation', width: 50 },
];

const redisInfoColumns = [
  { title: '实例', dataIndex: 'instance_id', key: 'instance_id', width: 400 },
  { title: '密码', dataIndex: 'pwd', key: 'pwd' },
  { title: '索引', dataIndex: 'idx', key: 'idx', width: 650 },
  { title: '操作', key: 'operation', width: 50 },
];

const moduleColumns = [
  { title: '模块名称', dataIndex: 'module', key: 'module' },
  { title: '模块机器', dataIndex: 'hosts', key: 'hosts' },
  { title: '进程数', dataIndex: 'process_num', key: 'process_num', width: 100 },
];

const deleteMysqlInfo = (record: any) => {
  const index = formData.value.mysqlInfos.indexOf(record);
  if (index !== -1) {
    formData.value.mysqlInfos.splice(index, 1);
  }
};

const deleteRedisInfo = (record: any) => {
  const index = formData.value.redisInfos.indexOf(record);
  if (index !== -1) {
    formData.value.redisInfos.splice(index, 1);
  }
};

const addMysqlInfo = () => {
  formData.value.mysqlInfos.push({
    instance_id: '',
    mysql_user: 'work',
    mysql_pwd: '',
    table_idx: [],
  });
};

const addRedisInfo = () => {
  formData.value.redisInfos.push({
    instance_id: '',
    redis_pwd: '',
    idx: [],
  });
};

const getMysqlOptions = async () => {
  mysqlLoading.value = true;
  const data = await getRegionMysql();
  mysqlOptions.value = data.map((item: any) => ({
    value: item.db_cluster_id,
    label: item.db_cluster_description,
  }));
  mysqlLoading.value = false;
};

const getRedisOptions = async () => {
  redisLoading.value = true;
  const data = await getRegionRedis();
  redisOptions.value = data.map((item: any) => ({
    value: item.instance_id,
    label: item.instance_name,
  }));
  redisLoading.value = false;
};

const getMysqlIdxOptions = async () => {
  mysqlIdxLoading.value = true;
  const data = await getRegionMysqlIdx();
  allMysqlIdx.value = data.map((item: any) => ({
    value: item.idx,
    label: item.name,
  }));
  mysqlIdxLoading.value = false;
};

const getRedisIdxOptions = async () => {
  redisIdxLoading.value = true;
  const data = await getRegionRedisIdx();
  allRedisIdx.value = data.map((item: any) => ({
    value: item.idx,
    label: item.name,
  }));
  redisIdxLoading.value = false;
};

// 等待500ms后再获取
const getIdleRegions = useDebounceFn(async () => {
  if (!formData.value.region) {
    return;
  }
  await getRegionIdleHosts(formData.value.region).then((data) => {
    regionHosts.value = data.list;
  });
}, 500);

onMounted(async () => {
  await Promise.all([
    getMysqlOptions(),
    getRedisOptions(),
    getMysqlIdxOptions(),
    getRedisIdxOptions(),
  ]);
});

const handleSelectAll = (
  selectedOptions: SelectValue[],
  allOptions: SelectOption[],
) => {
  const allValues = allOptions.map((item) => item.value);
  selectedOptions.push(...allValues);
};

const handleDeselectAll = (selectedOptions: SelectValue[]) => {
  selectedOptions.length = 0;
};
</script>

<template>
  <Form :model="formData" :label-col="{ style: { width: '100px' } }">
    <!-- 区服名称 -->
    <FormItem label="区服名称" name="region" required>
      <Input
        v-model:value="formData.region"
        placeholder="请输入区服"
        @input="getIdleRegions"
      />
    </FormItem>

    <!-- 默认分支 -->
    <FormItem label="默认分支" name="branch" required>
      <Input v-model:value="formData.branch" placeholder="请输入分支" />
    </FormItem>

    <!-- MySQL -->
    <FormItem label="MySQL" required>
      <Table
        v-if="formData.mysqlInfos.length > 0"
        :columns="mysqlInfoColumns"
        :data-source="formData.mysqlInfos"
        size="small"
        :pagination="false"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'instance_id'">
            <Select
              v-model:value="record.instance_id"
              class="w-full"
              :options="mysqlOptions"
              :loading="mysqlLoading"
              option-filter-prop="label"
              show-search
            />
          </template>
          <template v-if="column.key === 'user'">
            <Input
              :value="record.mysql_user"
              placeholder="请输入用户"
              default-value="work"
            />
          </template>
          <template v-if="column.key === 'pwd'">
            <InputPassword
              v-model:value="record.mysql_pwd"
              placeholder="请输入密码"
              autocomplete="off"
            />
          </template>
          <template v-if="column.key === 'idx'">
            <Select
              v-model:value="record.table_idx"
              class="w-full"
              mode="multiple"
              :options="mysqlIdxOptions"
              :loading="mysqlIdxLoading"
              show-search
            >
              <!-- antd回显标签功能有问题，暂时用这种方法 -->
              <template #tagRender="{ value, closable, onClose }">
                <Tag
                  :closable="closable"
                  style="margin-right: 3px"
                  @close="onClose"
                >
                  {{ allMysqlIdx.find((item) => item.value === value)?.label }}
                </Tag>
              </template>
              <template #dropdownRender="{ menuNode: menu }">
                <VNodes :vnodes="menu" />
                <Divider style="margin: 4px 0" />
                <div class="dropdown-header">
                  <Button
                    type="link"
                    size="small"
                    @click="handleSelectAll(record.table_idx, mysqlIdxOptions)"
                  >
                    全选
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    @click="handleDeselectAll(record.table_idx)"
                  >
                    取消选择
                  </Button>
                </div>
              </template>
            </Select>
          </template>
          <template v-if="column.key === 'operation'">
            <Button type="link" @click="deleteMysqlInfo(record)"> 删除 </Button>
          </template>
        </template>
      </Table>
      <Dropdown trigger="click">
        <Button class="w-full" type="dashed" @click="addMysqlInfo">
          添加实例
        </Button>
      </Dropdown>
    </FormItem>

    <FormItem label="Redis" required>
      <Table
        v-if="formData.redisInfos.length > 0"
        :columns="redisInfoColumns"
        :data-source="formData.redisInfos"
        size="small"
        :pagination="false"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'instance_id'">
            <Select
              v-model:value="record.instance_id"
              class="w-full"
              :options="redisOptions"
              :loading="redisLoading"
              option-filter-prop="label"
              show-search
            />
          </template>
          <template v-if="column.key === 'pwd'">
            <InputPassword
              v-model:value="record.redis_pwd"
              placeholder="请输入密码"
              autocomplete="off"
            />
          </template>
          <template v-if="column.key === 'idx'">
            <Select
              v-model:value="record.idx"
              class="w-full"
              mode="multiple"
              :options="redisIdxOption"
              :loading="redisIdxLoading"
              option-filter-prop="label"
              show-search
            >
              <!-- antd回显标签功能有问题，暂时用这种方法 -->
              <template #tagRender="{ value, closable, onClose }">
                <Tag
                  :closable="closable"
                  style="margin-right: 3px"
                  @close="onClose"
                >
                  {{ allRedisIdx.find((item) => item.value === value)?.label }}
                </Tag>
              </template>
              <template #dropdownRender="{ menuNode: menu }">
                <VNodes :vnodes="menu" />
                <Divider style="margin: 4px 0" />
                <div class="dropdown-header">
                  <Button
                    type="link"
                    size="small"
                    @click="handleSelectAll(record.idx, redisIdxOption)"
                  >
                    全选
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    @click="handleDeselectAll(record.idx)"
                  >
                    取消选择
                  </Button>
                </div>
              </template>
            </Select>
          </template>
          <template v-if="column.key === 'operation'">
            <Button type="link" @click="deleteRedisInfo(record)"> 删除 </Button>
          </template>
        </template>
      </Table>
      <Dropdown trigger="click">
        <Button class="w-full" type="dashed" @click="addRedisInfo">
          添加实例
        </Button>
      </Dropdown>
    </FormItem>

    <!-- 区服模块机器 -->
    <FormItem label="区服机器" required>
      <RadioGroup v-model:value="formData.deployMode">
        <RadioButton value="all_in_one">单机部署</RadioButton>
        <RadioButton value="deploy_by_modules">分模块部署</RadioButton>
      </RadioGroup>
    </FormItem>

    <template v-if="formData.deployMode === 'all_in_one'">
      <FormItem label="单机部署" required>
        <HostSelection :hosts="regionHosts" :value="formData.allInOneHosts" />
      </FormItem>
    </template>

    <template v-else-if="formData.deployMode === 'deploy_by_modules'">
      <FormItem label="分模块部署" required>
        <Table
          :columns="moduleColumns"
          :data-source="formData.moduleHosts"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'module'">
              {{ record.module }}
            </template>
            <template v-if="column.key === 'hosts'">
              <HostSelection :hosts="regionHosts" :value="record.hosts" />
            </template>
            <template v-if="column.key === 'process_num'">
              <InputNumber v-model:value="record.process_num" />
            </template>
          </template>
        </Table>
      </FormItem>
    </template>
  </Form>
</template>
