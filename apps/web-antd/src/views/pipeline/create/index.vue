<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { But<PERSON>, Card, message, Steps } from 'ant-design-vue';

import { createPipelineApi } from '#/api/engine/engine';
import { PipelineConfig } from '#/components/pipelineConfig';
import { GenerateDateStr } from '#/utils/date';

import CreateForm from './createForm.vue';
import {
  defaultFormData,
  getVariablesFromFormData,
  pipelineData,
} from './data';

const router = useRouter();
const formData = reactive(cloneDeep(defaultFormData));
const submit = () => {
  const name = `${formData.region}-创建区服-${GenerateDateStr(new Date())}`;
  const submitData = {
    stages: pipelineData.stages,
    name,
    description: name,
    variables: getVariablesFromFormData(formData),
  };

  createPipelineApi({
    pipeline: submitData,
  }).then((data) => {
    message.success('创建任务成功');
    router.push(`/pipelines/detail/${data.pipeline_id}`);
  });
};

const current = ref<number>(0);
const next = () => {
  current.value++;
};
const prev = () => {
  current.value--;
};
const steps = [
  {
    title: '填写参数',
  },
  {
    title: '编辑流水线',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));
</script>

<template>
  <Page auto-content-height description="创建新区服" title="创建区服">
    <Card style="height: calc(100% - 50px)" :body-style="{ height: '100%' }">
      <Steps :current="current" :items="items" />
      <div v-if="current === 0" class="mt-12">
        <CreateForm :form-data="formData" />
      </div>
      <div v-if="current === 1" class="mt-16">
        <PipelineConfig :pipeline-data="pipelineData" />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current < steps.length - 1" type="primary" @click="next">
        下一步
      </Button>
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        创建任务
      </Button>
    </div>
  </Page>
</template>
