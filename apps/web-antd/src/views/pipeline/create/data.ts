import type { FormData } from './type';

export const pipelineData = {
  stages: [
    {
      id: 'stage_id_1',
      name: '创建区服',
      is_enabled: true,
      status: 'READY',
      tasks: [
        {
          id: 'task_id_11',
          name: '创建区服',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_111',
              name: '创建区服',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'region.create_region',
                input: [
                  {
                    name: '区服',
                    key: 'region',
                    value: 'region',
                    type: 'ref',
                  },
                  {
                    name: '初始分支',
                    key: 'branch',
                    value: 'branch',
                    type: 'ref',
                  },
                  {
                    name: '部署模式',
                    key: 'deploy_mode',
                    value: 'deploy_mode',
                    type: 'ref',
                  },
                  {
                    name: 'MySQL实例',
                    key: 'mysql_infos',
                    value: 'mysql_infos',
                    type: 'ref',
                  },
                  {
                    name: 'Redis实例',
                    key: 'redis_infos',
                    value: 'redis_infos',
                    type: 'ref',
                  },
                  {
                    name: '单机部署',
                    key: 'all_in_one_hosts',
                    value: 'all_in_one_hosts',
                    type: 'ref',
                  },
                  {
                    name: '分模块部署',
                    key: 'module_hosts',
                    value: 'module_hosts',
                    type: 'ref',
                  },
                ],
              },
            },
          ],
        },
        {
          id: 'task_id_12',
          name: '创建模块',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_121',
              name: '创建模块',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'region.create_modules_server',
                input: [
                  {
                    name: '区服',
                    key: 'region',
                    value: 'region',
                    type: 'ref',
                  },
                  {
                    name: '部署模式',
                    key: 'deploy_mode',
                    value: 'deploy_mode',
                    type: 'ref',
                  },
                  {
                    name: '单机部署',
                    key: 'all_in_one_hosts',
                    value: 'all_in_one_hosts',
                    type: 'ref',
                  },
                  {
                    name: '模块信息',
                    key: 'module_hosts',
                    value: 'module_hosts',
                    type: 'ref',
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};

const moduleOptions = [
  { value: 'dp', label: 'dispatch' },
  { value: 'muip', label: 'muipserver' },
  { value: 'oa', label: 'oaserver' },
  { value: 'ns', label: 'nodeserver' },
  { value: 'gs', label: 'gameserver' },
  { value: 'rank', label: 'rankserver' },
  { value: 'gate', label: 'gateserver' },
  { value: 'mail', label: 'mailserver' },
  { value: 'sns', label: 'snsserver' },
  { value: 'fightmgr', label: 'fightmgrserver' },
  { value: 'match', label: 'matchserver' },
  { value: 'dg', label: 'dbgate' },
  { value: 'ufight', label: 'ufightserver' },
  { value: 'fight', label: 'fightserver' },
  { value: 'room', label: 'roomserver' },
];

// 表单数据
export const defaultFormData: FormData = {
  region: '',
  branch: '',
  deployMode: 'all_in_one',
  mysqlInfos: [],
  redisInfos: [],
  allInOneHosts: [],
  moduleHosts: moduleOptions.map((item) => ({
    hosts: [],
    module: item.label,
    process_num: 1,
  })),
};

export function getVariablesFromFormData(formData: FormData) {
  return [
    {
      name: '区服',
      key: 'region',
      type: 'plain',
      value: formData.region,
    },
    {
      name: '默认分支',
      key: 'branch',
      type: 'plain',
      value: formData.branch,
    },
    {
      name: 'MySQL实例',
      key: 'mysql_infos',
      type: 'plain',
      value: formData.mysqlInfos,
    },
    {
      name: 'Redis实例',
      key: 'redis_infos',
      type: 'plain',
      value: formData.redisInfos,
    },
    {
      name: '部署模式',
      key: 'deploy_mode',
      type: 'plain',
      value: formData.deployMode,
    },
    {
      name: '单机部署',
      key: 'all_in_one_hosts',
      type: 'plain',
      value: formData.allInOneHosts,
    },
    {
      name: '分模块部署',
      key: 'module_hosts',
      type: 'plain',
      value: formData.moduleHosts,
    },
  ];
}
