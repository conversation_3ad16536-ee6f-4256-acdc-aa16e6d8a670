<script lang="ts" setup>
import type { MenuProps } from 'ant-design-vue';
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';

import type { SelectOption } from '@vben/types';

import type { FormData } from './type';

import type { HostInfo } from '#/components/hostSelection/type';

import { computed, onMounted, ref } from 'vue';

import { DownOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Dropdown,
  Form,
  FormItem,
  Menu,
  MenuItem,
  Select,
  Table,
  Tooltip,
} from 'ant-design-vue';

import { getRegionHostsApi, getRegionsNamesApi } from '#/api/region/region';
import { HostSelection } from '#/components/hostSelection';

const formData = defineModel<FormData>('formData', { required: true });

const moduleOptions = [
  { value: 'dp', label: 'dispatch' },
  { value: 'muip', label: 'muipserver' },
  { value: 'oa', label: 'oaserver' },
  { value: 'ns', label: 'nodeserver' },
  { value: 'gs', label: 'gameserver' },
  { value: 'rank', label: 'rankserver' },
  { value: 'gate', label: 'gateserver' },
  { value: 'mail', label: 'mailserver' },
  { value: 'sns', label: 'snsserver' },
  { value: 'fightmgr', label: 'fightmgrserver' },
  { value: 'match', label: 'matchserver' },
  { value: 'dg', label: 'dbgate' },
  { value: 'ufight', label: 'ufightserver' },
  { value: 'fight', label: 'fightserver' },
  { value: 'room', label: 'roomserver' },
];
const regionOptions = ref<SelectOption[]>([]);
const regionHosts = ref<HostInfo[]>([]);

const computedOptions = computed(() => {
  return moduleOptions.filter((item) => {
    if (formData.value.moduleHosts.length === 0) {
      return true;
    }
    return !formData.value.moduleHosts.some(
      (module) => module.module === item.label,
    );
  });
});

// 表格列配置
const moduleColumns = [
  { title: '模块名称', dataIndex: 'module', key: 'module' },
  { title: '模块机器', dataIndex: 'hosts', key: 'hosts' },
  { title: '操作', key: 'operation' },
];

const deleteModule = (record: any) => {
  const index = formData.value.moduleHosts.indexOf(record);
  if (index !== -1) {
    formData.value.moduleHosts.splice(index, 1);
  }
};

const addModule: MenuProps['onClick'] = (e) => {
  formData.value.moduleHosts.push({
    module: String(e.key),
    hosts: [],
    process_num: 1,
  });
};

const getAllRegions = () => {
  getRegionsNamesApi().then((res) => {
    regionOptions.value = res.list.map((item: string) => ({
      label: item,
      value: item,
    }));
  });
};

onMounted(() => {
  getAllRegions();
  if (formData.value.region) {
    getRegionHosts(formData.value.region);
  }
});

const getRegionHosts = async (region: string) => {
  await getRegionHostsApi(region)
    .then((res) => {
      regionHosts.value = res.list;
    })
    .finally(() => {});
};

const handleRegionChange = async (
  value: SelectValue,
  _: DefaultOptionType | DefaultOptionType[],
) => {
  await getRegionHosts(`${value}`);
};
</script>

<template>
  <Form
    :model="formData"
    autocomplete="off"
    :label-col="{ style: { width: '100px' } }"
  >
    <!-- 区服名称 -->
    <FormItem label="区服名称" name="region" required>
      <Select
        v-model:value="formData.region"
        placeholder="请选择区服名称"
        :options="regionOptions"
        @change="handleRegionChange"
      />
    </FormItem>

    <!-- 模块选择 -->
    <FormItem label="模块选择" required>
      <Table
        v-if="formData.moduleHosts.length > 0"
        :columns="moduleColumns"
        :data-source="formData.moduleHosts"
        size="small"
        :pagination="false"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'module'">
            {{ record.module }}
          </template>
          <template v-if="column.key === 'hosts'">
            <HostSelection :hosts="regionHosts" :value="record.hosts" />
          </template>
          <template v-if="column.key === 'operation'">
            <Button type="link" @click="deleteModule(record)"> 删除 </Button>
          </template>
        </template>
      </Table>
      <Dropdown trigger="click">
        <template #overlay>
          <Menu @click="addModule">
            <MenuItem v-for="option in computedOptions" :key="option.label">
              {{ option.label }}
            </MenuItem>
          </Menu>
        </template>
        <Button class="w-full" type="dashed">
          添加模块
          <DownOutlined />
        </Button>
      </Dropdown>
    </FormItem>

    <!-- 重载机器 -->
    <FormItem label="重载机器">
      <template #tooltip>
        <Tooltip class="ml-1">
          <template #title>
            <p>下线gameserver重载其他gameserver和所有gateserver</p>
          </template>
          <QuestionCircleOutlined />
        </Tooltip>
      </template>
      <HostSelection :hosts="regionHosts" :value="formData.reloadHosts" />
    </FormItem>
  </Form>
</template>
