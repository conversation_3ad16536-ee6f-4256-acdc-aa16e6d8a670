<script lang="ts" setup>
import type { FormData } from './type';
import { h } from 'vue';
import { getRegionsNamesApi } from '#/api/region/region';
import { useVbenForm } from '#/adapter/form';
import { ModuleSelection } from '#/components/moduleSelection';
import { RegionVersionValues } from '#/components/regionVersionValues';
import { RegionHostSelection } from '#/components/regionHostSelection';

const formData = defineModel<FormData>('formData', { required: true });

// 定义表单schema
const formSchema = [
  {
    component: 'ApiSelect',
    fieldName: 'region',
    label: '区服名称',
    componentProps: {
      placeholder: '请选择区服名称',
      api: getRegionsNamesApi,
      resultField: 'list',
      immediate: true,
      value: formData.value.region,
      onChange: (val: any) => {
        formData.value.region = val;
      },
    },
    rules: 'required',
  },
  {
    component: h(ModuleSelection, {
      showPatchId: true,
      region: formData.value.region,
      value: formData.value.moduleHosts,
      'onUpdate:value': (val: any) => {
        formData.value.moduleHosts = val;
      },
    }),
    fieldName: 'moduleHosts',
    label: '模块选择',
    dependencies: {
      triggerFields: ['region'],
      componentProps(values: any) {
        if (!values.region) {
          return {};
        }
        return {
          region: values.region,
        };
      },
    },
    rules: 'required',
  },
  {
    component: h(RegionHostSelection, {
      region: formData.value.region,
      value: formData.value.reloadHosts,
      'onUpdate:value': (newValue) => {
        console.log('expand Form value is :', newValue);
        formData.value.reloadHosts = newValue;
      },
    }),
    fieldName: 'reloadHosts',
    label: '重载机器',
    help: () => {
      return h('div', null, [
        h('p', null, '上线gameserver需要重载gateserver'),
        h('p', null, '上线dispatch需要重载gateserver'),
        h('p', null, '上线nodeserver需要重载所有其他模块'),
        h('p', null, '上线fightmgr需要重载ufight'),
      ]);
    },
    dependencies: {
      triggerFields: ['region'],
      componentProps(values: any) {
        if (!values.region) {
          return {};
        }
        return {
          region: values.region,
        };
      },
    },
  },
  {
    component: h(RegionVersionValues, {
      region: formData.value.region,
      value: formData.value.versionValues,
      'onUpdate:value': (val: any) => {
        formData.value.versionValues = val;
      },
    }),
    fieldName: 'versionValues',
    label: '区服数值',
    dependencies: {
      triggerFields: ['region'],
      componentProps(values: any) {
        if (!values.region) {
          return {};
        }
        return {
          region: values.region,
        };
      },
    },
    rules: 'required',
  },
];

// 使用useVbenForm创建表单
const [Form] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: formSchema,
  showDefaultActions: false,
});
</script>

<template>
  <Form />
</template>
