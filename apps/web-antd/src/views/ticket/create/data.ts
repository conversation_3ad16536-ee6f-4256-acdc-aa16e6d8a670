import type { SelectOption } from '@vben/types';

export const FORM_TYPES = {
  EXPAND_REGION: 'expand_region',
  DOWNSCALE_REGION: 'downscale_region',
  CREATE_REGION: 'create_region',
};

export const templateOptions = [
  { label: '创建区服', value: FORM_TYPES.CREATE_REGION },
  { label: '区服扩容', value: FORM_TYPES.EXPAND_REGION },
  { label: '区服缩容', value: FORM_TYPES.DOWNSCALE_REGION },
] as SelectOption[];
