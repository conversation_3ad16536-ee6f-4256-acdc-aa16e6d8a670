<script lang="ts" setup>
import type { BasicForm } from './type';

import { Form, FormItem, Input, Select, Textarea } from 'ant-design-vue';

import { templateOptions } from './data';

const value = defineModel<BasicForm>('value', { required: true });
</script>

<template>
  <Form :model="value" :label-col="{ style: { width: '100px' } }">
    <!-- 工单名称 -->
    <FormItem label="工单名称" name="name" required>
      <Input v-model:value="value.name" placeholder="请输入工单名称" />
    </FormItem>

    <!-- 工单备注 -->
    <FormItem label="工单备注" name="description">
      <Textarea
        v-model:value="value.description"
        placeholder="请输入工单备注信息"
        :rows="4"
      />
    </FormItem>

    <!-- 流水线模板选择 -->
    <FormItem label="流水线模板" name="template" required>
      <Select
        v-model:value="value.template"
        placeholder="请选择模板"
        :options="templateOptions"
        show-search
        option-filter-prop="label"
      />
    </FormItem>
  </Form>
</template>
