<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { cloneDeep } from '@vben/utils';

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { <PERSON><PERSON>, Collapse, CollapsePanel } from 'ant-design-vue';

import { defaultFormData as createDefaultFormData } from '#/views/pipeline/create/data';
import { defaultFormData as downscaleDefaultFormData } from '#/views/pipeline/downscale/data';
import { defaultFormData as expandDefaultFormData } from '#/views/pipeline/expand/data';

import CreateForm from '../../pipeline/create/createForm.vue';
import DownscaleForm from '../../pipeline/downscale/downscaleForm.vue';
import ExpandForm from '../../pipeline/expand/expandForm.vue';
import { FORM_TYPES, templateOptions } from './data';

const props = defineProps<{
  template: string;
}>();
const forms = defineModel<any[]>('value', { required: true });

const activeKey = ref<string[]>([]);
// Generate default data based on form type
const getDefaultFormData = (type: string) => {
  switch (type) {
    case FORM_TYPES.CREATE_REGION: {
      return cloneDeep(createDefaultFormData);
    }
    case FORM_TYPES.DOWNSCALE_REGION: {
      return cloneDeep(downscaleDefaultFormData);
    }
    case FORM_TYPES.EXPAND_REGION: {
      return cloneDeep(expandDefaultFormData);
    }
    default: {
      return {};
    }
  }
};

// Add a new form
const addForm = (type: string) => {
  forms.value.push({
    data: getDefaultFormData(type),
  });
  activeKey.value.push(String(forms.value.length));
};

// Remove a form by id
const removeForm = (id: string) => {
  const index = Number.parseInt(id) - 1;
  if (index !== -1) {
    forms.value.splice(index, 1);
  }
  activeKey.value = activeKey.value.filter((key) => key !== String(index + 1));
};

// Add initial form
if (forms.value.length === 0) {
  addForm(props.template);
  activeKey.value = ['1'];
}

const templateLabel = computed(() => {
  return templateOptions.find((item) => item.value === props.template)?.label;
});

onMounted(() => {
  activeKey.value = forms.value.map((_, index) => String(index + 1));
});
</script>

<template>
  <Collapse v-model:active-key="activeKey">
    <CollapsePanel
      v-for="(form, index) in forms"
      :key="index + 1"
      :header="`【${templateLabel}】表单${index + 1}`"
    >
      <ExpandForm
        v-if="props.template === FORM_TYPES.EXPAND_REGION"
        v-model:form-data="form.data"
      />
      <DownscaleForm
        v-else-if="props.template === FORM_TYPES.DOWNSCALE_REGION"
        v-model:form-data="form.data"
      />
      <CreateForm
        v-else-if="props.template === FORM_TYPES.CREATE_REGION"
        v-model:form-data="form.data"
      />
      <template #extra>
        <DeleteOutlined
          @click="
            (e: MouseEvent) => {
              e.stopPropagation();
              removeForm(form.id);
            }
          "
        />
      </template>
    </CollapsePanel>
  </Collapse>
  <Button type="dashed" @click="addForm(props.template)" class="w-full">
    <PlusOutlined /> 添加表单
  </Button>
</template>
