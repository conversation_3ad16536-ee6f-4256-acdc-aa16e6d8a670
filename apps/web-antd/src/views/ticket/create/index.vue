<script setup lang="ts">
import type { BasicForm } from './type';

import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { But<PERSON>, Card, message, Steps } from 'ant-design-vue';

import { createTicketApi } from '#/api/ticket/ticket';
import { GenerateDateStr } from '#/utils/date';
import {
  pipelineData as createPipelineData,
  getVariablesFromFormData as getCreateVariablesFromFormData,
} from '#/views/pipeline/create/data';
import {
  pipelineData as downscalePipelineData,
  getVariablesFromFormData as getDownscaleVariablesFromFormData,
} from '#/views/pipeline/downscale/data';
import {
  pipelineData as expandPipelineData,
  getVariablesFromFormData as getExpandVariablesFromFormData,
} from '#/views/pipeline/expand/data';

import BasicInfoForm from './basicInfoForm.vue';
import ConfirmInfo from './confirmInfo.vue';
import PipelineVariablesForm from './pipelineVariablesForm.vue';

const router = useRouter();
const current = ref<number>(0);

const next = () => {
  current.value++;
};
const prev = () => {
  current.value--;
};
const steps = [
  {
    title: '填写工单基础信息',
  },
  {
    title: '填写流程参数',
  },
  {
    title: '确认信息',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));
const basicForm = ref<BasicForm>({
  name: '',
  description: '',
  template: '',
});

const forms = reactive<any[]>([]);

const getPipelineConfig = () => {
  switch (basicForm.value.template) {
    case 'create_region': {
      return createPipelineData;
    }
    case 'downscale_region': {
      return downscalePipelineData;
    }
    case 'expand_region': {
      return expandPipelineData;
    }
    default: {
      return {
        stages: [],
      };
    }
  }
};

const getPipelinesData = () => {
  const prefix = `【工单${basicForm.value.name}】`;
  switch (basicForm.value.template) {
    case 'create_region': {
      return forms.map((item) => {
        return {
          name: `${prefix}${item.data.region}-创建区服-${GenerateDateStr(new Date())}`,
          variables: getCreateVariablesFromFormData(item.data),
        };
      });
    }
    case 'downscale_region': {
      return forms.map((item) => {
        return {
          name: `${prefix}${item.data.region}-缩容区服-${GenerateDateStr(new Date())}`,
          variables: getDownscaleVariablesFromFormData(item.data),
        };
      });
    }
    case 'expand_region': {
      return forms.map((item) => {
        return {
          name: `${prefix}${item.data.region}-扩容区服-${GenerateDateStr(new Date())}`,
          variables: getExpandVariablesFromFormData(item.data),
        };
      });
    }
    default: {
      return [];
    }
  }
};

const submit = () => {
  const submitData = {
    stages: getPipelineConfig().stages,
    name: basicForm.value.name,
    description: basicForm.value.description,
    pipelines: getPipelinesData(),
  };
  createTicketApi(submitData).then((data) => {
    message.success('创建工单成功');
    router.push(`/tickets/detail/${data.id}`);
  });
};
</script>
<template>
  <Page auto-content-height title="创建工单" description="根据流程模板创建工单">
    <Card style="height: calc(100% - 50px)" :body-style="{ height: '100%' }">
      <Steps :current="current" :items="items" />
      <div v-if="current === 0" class="mt-12">
        <BasicInfoForm v-model:value="basicForm" />
      </div>
      <div
        v-if="current === 1"
        class="mt-12"
        style="height: calc(100% - 100px); overflow-y: auto"
      >
        <PipelineVariablesForm
          :template="basicForm.template"
          v-model:value="forms"
        />
      </div>
      <div v-if="current === 2" class="mt-12">
        <ConfirmInfo
          :basic-info="basicForm"
          :pipeline-data="getPipelineConfig()"
        />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current < steps.length - 1"
        type="primary"
        @click="next"
        class="mr"
      >
        下一步
      </Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        创建任务
      </Button>
    </div>
  </Page>
</template>
