<script lang="ts" setup>
import { Card, Descriptions, DescriptionsItem } from 'ant-design-vue';

import { PipelineConfig } from '#/components/pipelineConfig';

const props = defineProps({
  basicInfo: {
    type: Object,
    required: true,
    default: () => ({
      name: '',
      description: '',
      template: '',
    }),
  },
  pipelineData: {
    type: Object,
    required: true,
    default: () => ({
      stages: [],
    }),
  },
});

// 根据表单类型获取阶段名称
const getTemplateName = (template: string) => {
  switch (template) {
    case 'create_region': {
      return '创建区服';
    }
    case 'downscale_region': {
      return '区服缩容';
    }
    case 'expand_region': {
      return '区服扩容';
    }
    default: {
      return '未知操作';
    }
  }
};
</script>

<template>
  <div>
    <Card title="工单基本信息" class="mb-4">
      <Descriptions bordered :column="2">
        <DescriptionsItem label="工单名称">
          {{ props.basicInfo.name }}
        </DescriptionsItem>
        <DescriptionsItem label="工单模板">
          {{ getTemplateName(props.basicInfo.template) }}
        </DescriptionsItem>
        <DescriptionsItem label="工单描述" :span="2">
          {{ props.basicInfo.description }}
        </DescriptionsItem>
      </Descriptions>
    </Card>
    <Card title="工单流程预览">
      <PipelineConfig :pipeline-data="props.pipelineData" />
    </Card>
  </div>
</template>
<style scoped>
/* 覆盖流水线配置组件中虚线的位置 */
::v-deep .scroll-container::before {
  top: 103px;
}
</style>
