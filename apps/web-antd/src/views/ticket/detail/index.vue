<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Card,
  Descriptions,
  DescriptionsItem,
  Divider,
  message,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getTicketApi } from '#/api/ticket/ticket';
import { PipelineAction } from '#/components/pipelineAction';
import { StatusTag } from '#/components/statusTag';

const route = useRoute();
const ticketId = route.params.id;
const spinning = ref<boolean>(false);

// 工单详情数据
const ticketData = reactive({
  id: '',
  name: '',
  description: '',
  template: '',
  status: '',
  created_by: '',
  created_at: '',
  updated_by: '',
  updated_at: '',
  pipelines: [],
});

// 表格配置
const gridOptions: VxeGridProps = {
  height: 400,
  columns: [
    { field: 'name', title: '流程名称', width: '500' },
    {
      field: 'region',
      title: '区服',
      formatter({ row }) {
        return (
          row.variables?.find((item: any) => item.key === 'region')?.value ||
          '-'
        );
      },
    },
    {
      field: 'status',
      title: '状态',
      slots: { default: 'status' },
    },
    {
      field: 'current_step',
      title: '当前步骤',
      width: '300',
      formatter({ row }) {
        if (!row.stages || row.stages.length === 0) return '-';

        const currentStage = row.stages[row.current_stage_idx];
        if (!currentStage) return '-';

        const stageName = currentStage.name;
        const taskName = currentStage.tasks[row.current_task_idx]?.name || '-';

        return `步骤${row.current_stage_idx + 1}【${stageName}】 - 任务${row.current_task_idx + 1}【${taskName}】`;
      },
    },
    {
      field: 'created_at',
      title: '创建时间',
    },
    {
      field: 'updated_at',
      title: '更新时间',
    },
    {
      title: '操作',
      slots: { default: 'actions' },
    },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
};

const [PipelinesGrid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 获取工单详情
const getTicketDetail = async () => {
  spinning.value = true;
  try {
    const data = await getTicketApi(String(ticketId));

    // 更新工单基本信息
    ticketData.id = data.id;
    ticketData.name = data.name;
    ticketData.description = data.description;
    ticketData.template = data.template;
    ticketData.status = data.status;
    ticketData.created_by = data.created_by;
    ticketData.created_at = data.created_at;
    ticketData.updated_by = data.updated_by;
    ticketData.updated_at = data.updated_at;

    // 更新流程列表
    ticketData.pipelines = data.pipelines || [];

    // 更新表格数据
    gridOptions.data = ticketData.pipelines;
    gridApi.setState({ gridOptions });
  } catch (error) {
    console.error('获取工单详情失败', error);
    message.error('获取工单详情失败');
  } finally {
    spinning.value = false;
  }
};

onMounted(() => {
  getTicketDetail();
});
</script>

<template>
  <Page
    auto-content-height
    title="工单详情"
    description="查看工单及其包含的流程"
    v-spinning="spinning"
  >
    <Card>
      <Descriptions bordered>
        <DescriptionsItem label="工单名称">
          {{ ticketData.name }}
        </DescriptionsItem>
        <DescriptionsItem label="工单状态">
          <StatusTag :status="ticketData.status" />
        </DescriptionsItem>
        <DescriptionsItem label="工单描述" :span="3">
          {{ ticketData.description }}
        </DescriptionsItem>
        <DescriptionsItem label="创建人">
          {{ ticketData.created_by }}
        </DescriptionsItem>
        <DescriptionsItem label="创建时间">
          {{ ticketData.created_at }}
        </DescriptionsItem>
        <DescriptionsItem label="更新人">
          {{ ticketData.updated_by }}
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          {{ ticketData.updated_at }}
        </DescriptionsItem>
      </Descriptions>

      <Divider>流程列表</Divider>

      <PipelinesGrid>
        <template #status="{ row }">
          <StatusTag :status="row.status" />
        </template>
        <template #actions="{ row }">
          <PipelineAction
            action="view"
            :pipeline-id="row.id"
            :pipeline-name="row.name"
          />
          <template v-for="action in row.actions" :key="action">
            <PipelineAction
              :action="action"
              :pipeline-id="row.id"
              :pipeline-name="row.name"
              :callback="getTicketDetail"
            />
          </template>
        </template>
      </PipelinesGrid>
    </Card>
  </Page>
</template>
