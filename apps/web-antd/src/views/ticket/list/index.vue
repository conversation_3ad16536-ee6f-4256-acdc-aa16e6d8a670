<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { Button, message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import { actionPipelineApi, queryPipelineApi } from '#/api/engine/engine';
import { actionTicketApi, queryTicketApi } from '#/api/ticket/ticket';
import { StatusTag } from '#/components/statusTag';

const router = useRouter();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '工单名称',
    },
    {
      component: 'Input',
      fieldName: 'description',
      label: '工单描述',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    {
      title: '工单名称',
      field: 'name',
    },
    {
      title: '备注',
      field: 'description',
    },
    {
      title: '操作区服',
      field: 'regions',
      slots: { default: 'regions' },
    },
    {
      title: '创建人',
      field: 'created_by',
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    {
      title: '创建时间',
      field: 'created_at',
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    {
      title: '状态',
      field: 'status',
      slots: { default: 'status' },
    },
    { title: '操作', field: 'actions', slots: { default: 'actions' } },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryTicketApi(
          formValues.name,
          formValues.description,
          page.currentPage,
          page.pageSize,
        ).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const handleAction = async (action: any, id: string) => {
  switch (action) {
    case 'delete': {
      console.warn('删除');
      break;
    }
    case 'start':
    case 'stop': {
      await actionTicketApi(id, action).then(() => {
        message.success('操作成功');
        gridApi.query();
      });
      break;
    }
    case 'view': {
      router.push(`/tickets/detail/${id}`);
      break;
    }
    default: {
      break;
    }
  }
};
</script>

<template>
  <Page auto-content-height title="工单列表" description="展示执行的工单">
    <Grid>
      <template #status="{ row }">
        <StatusTag :status="row.status" />
      </template>
      <template #regions="{ row }">
        <Tag
          v-for="region in [...new Set(row.regions)]"
          :key="region as string"
        >
          {{ region }}
        </Tag>
      </template>
      <template #actions="{ row }">
        <Button @click="handleAction('view', row.id)" class="mr-2">
          查看
        </Button>
      </template>
    </Grid>
  </Page>
</template>
