import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:rectangle-ellipsis',
      title: '自定义流程',
    },
    name: 'custom_pipeline',
    path: '/pipelines/custom/create',
    children: [
      {
        meta: {
          icon: 'lucide:rectangle-ellipsis',
          title: '创建流程',
        },
        name: 'custom_pipeline_create',
        path: '/custom_pipelines/create',
        component: () => import('#/views/custom_pipeline/create/index.vue'),
      },
    ],
  },
];

export default routes;
